import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { isClickUpError } from '@/data/types/clickup.types';

export const tasksPatchSpaceIdJob = client.defineJob({
  id: 'tasks-patch-space-id-job',
  name: 'Tasks Patch Space ID Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    // 1. FETCH ALL TASKS WITHOUT SPACE ID
    const tasksWithoutSpaceId = await io.runTask('fetch-tasks-without-space-id', async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('tasks')
        .select('id, clickup_task_id, clickup_list_id')
        .is('clickup_space_id', null)
        .not('clickup_list_id', 'is', null);

      if (error) {
        throw new Error(`Error fetching tasks without space ID: ${JSON.stringify(error)}`);
      }

      return data || [];
    });

    await io.logger.info(`Found ${tasksWithoutSpaceId.length} tasks without space ID`);

    if (tasksWithoutSpaceId.length === 0) {
      await io.logger.info('No tasks to update');
      return;
    }

    // 2. CREATE A MAP OF LIST ID TO SPACE ID TO AVOID DUPLICATE API CALLS
    const listToSpaceMap = new Map<string, string>();
    const clickupClient = new ClickUpClient();

    // 3. PROCESS TASKS IN BATCHES
    const batchSize = 50;
    let processedCount = 0;
    let updatedCount = 0;

    for (let i = 0; i < tasksWithoutSpaceId.length; i += batchSize) {
      const batch = tasksWithoutSpaceId.slice(i, i + batchSize);
      
      await io.runTask(`process-batch-${Math.floor(i / batchSize)}`, async () => {
        for (const task of batch) {
          if (!task.clickup_list_id) {
            await io.logger.warn(`Task ${task.clickup_task_id} has no list ID, skipping`);
            continue;
          }

          let spaceId: string;

          // Check if we already have the space ID for this list
          if (listToSpaceMap.has(task.clickup_list_id)) {
            spaceId = listToSpaceMap.get(task.clickup_list_id)!;
          } else {
            // Fetch list information from ClickUp
            const listResponse = await clickupClient.getList(Number(task.clickup_list_id));

            if (isClickUpError(listResponse)) {
              await io.logger.error(`Error fetching list ${task.clickup_list_id}: ${JSON.stringify(listResponse)}`);
              continue;
            }

            spaceId = listResponse.space.id;
            listToSpaceMap.set(task.clickup_list_id, spaceId);
          }

          // Update task with space ID
          const supabase = createClient();
          const { error: updateError } = await supabase
            .from('tasks')
            .update({ clickup_space_id: Number(spaceId) })
            .eq('id', task.id);

          if (updateError) {
            await io.logger.error(`Error updating task ${task.clickup_task_id} with space ID: ${JSON.stringify(updateError)}`);
          } else {
            updatedCount++;
            await io.logger.info(`Updated task ${task.clickup_task_id} with space ID ${spaceId}`);
          }

          processedCount++;
        }
      });

      // Add a small delay between batches to avoid rate limiting
      if (i + batchSize < tasksWithoutSpaceId.length) {
        await io.wait(`batch-delay-${Math.floor(i / batchSize)}`, 1);
      }
    }

    await io.logger.info(`Processing complete. Processed: ${processedCount}, Updated: ${updatedCount}, Unique lists: ${listToSpaceMap.size}`);
  },
});
