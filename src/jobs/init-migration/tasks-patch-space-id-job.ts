import { invokeTrigger } from '@trigger.dev/sdk';
import { client } from '@/trigger';
import { createClient } from '@/data/supabase-server';
import { ClickUpClient } from '@/data/clickup-client';
import { ClickUpTask } from '@/data/types/clickup.types';

// Minimal task data to reduce memory usage
type TaskSpaceData = {
  id: string;
  space_id: string;
};

export const tasksPatchSpaceIdJob = client.defineJob({
  id: 'tasks-patch-space-id-job',
  name: 'Tasks Patch Space ID Job',
  version: '0.0.1',
  trigger: invokeTrigger(),
  run: async (payload, io, _) => {
    const clickupClient = new ClickUpClient();

    // 1. FETCH ALL SPACES
    const spaceIds = await io.runTask('fetch-all-clickup-spaces', async () => {
      const spacesResponse = await clickupClient.getSpaces();

      if ('err' in spacesResponse) {
        throw new Error(`Error fetching spaces: ${JSON.stringify(spacesResponse.err)}`);
      }

      return spacesResponse.spaces.map((space) => space.id);
    });

    await io.logger.info(`Found ${spaceIds.length} spaces to process`);

    // 2. FETCH ALL TASKS FROM ALL SPACES AND COLLECT MINIMAL DATA
    const allTasksWithSpaceId: TaskSpaceData[] = [];

    for (const spaceId of spaceIds) {
      await io.runTask(`fetch-tasks-from-space-${spaceId}`, async () => {
        let page = 0;
        let lastPage = false;
        let tasksFromSpace = 0;

        while (!lastPage) {
          const [curTasks, isLastPage] = await io.runTask(`fetch-space-${spaceId}-page-${page}`, async () => {
            const tasksResponse = await clickupClient.getTasksOfSpaces(page, [spaceId]);

            if ('err' in tasksResponse) {
              throw new Error(`Error fetching tasks from space ${spaceId}: ${JSON.stringify(tasksResponse)} at page ${page}`);
            }

            return [tasksResponse.tasks, tasksResponse.last_page];
          });

          // Extract only the minimal data we need
          const minimalTasks: TaskSpaceData[] = curTasks.map((task: ClickUpTask) => ({
            id: task.id,
            space_id: spaceId,
          }));

          allTasksWithSpaceId.push(...minimalTasks);
          tasksFromSpace += curTasks.length;

          await io.logger.info(`Fetched ${curTasks.length} tasks from space ${spaceId}, page ${page}`);

          lastPage = isLastPage;
          page++;
        }

        await io.logger.info(`Total tasks from space ${spaceId}: ${tasksFromSpace}`);
      });
    }

    await io.logger.info(`Total tasks fetched from ClickUp: ${allTasksWithSpaceId.length}`);

    // 3. UPDATE TASKS IN DATABASE IN BATCHES
    const batchSize = 100;
    let updatedCount = 0;

    for (let i = 0; i < allTasksWithSpaceId.length; i += batchSize) {
      const batch = allTasksWithSpaceId.slice(i, i + batchSize);

      await io.runTask(`update-batch-${Math.floor(i / batchSize)}`, async () => {
        const supabase = createClient();

        for (const taskData of batch) {
          const { error: updateError } = await supabase
            .from('tasks')
            .update({ clickup_space_id: Number(taskData.space_id) })
            .eq('clickup_task_id', taskData.id);

          if (updateError) {
            await io.logger.error(`Error updating task ${taskData.id} with space ID: ${JSON.stringify(updateError)}`);
          } else {
            updatedCount++;
          }
        }
      });

      // Add a small delay between batches to avoid overwhelming the database
      if (i + batchSize < allTasksWithSpaceId.length) {
        await io.wait(`batch-delay-${Math.floor(i / batchSize)}`, 1);
      }

      await io.logger.info(`Updated batch ${Math.floor(i / batchSize) + 1}, total updated so far: ${updatedCount}`);
    }

    await io.logger.info(`Processing complete. Total tasks updated: ${updatedCount} out of ${allTasksWithSpaceId.length}`);
  },
});
